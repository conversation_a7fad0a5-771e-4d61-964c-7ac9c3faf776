<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('referrals', function (Blueprint $table) {
            $table->id();
            $table->foreignId('referrer_id')->constrained('users')->cascadeOnDelete()->comment('Người giới thiệu');
            $table->foreignId('referee_id')->constrained('users')->cascadeOnDelete()->comment('Người được giới thiệu');
            $table->foreignId('tenant_id')->constrained()->cascadeOnDelete();
            $table->string('referral_code', 20)->comment('Mã giới thiệu được sử dụng');
            $table->timestamp('referred_at')->useCurrent()->comment('Thời gian đăng ký qua link giới thiệu');
            $table->timestamp('first_deposit_at')->nullable()->comment('Thời gian nạp tiền lần đầu');
            $table->decimal('first_deposit_amount', 15, 2)->nullable()->comment('Số tiền nạp lần đầu');
            $table->boolean('commission_paid')->default(false)->comment('Đã trả hoa hồng chưa');
            $table->boolean('bonus_paid')->default(false)->comment('Đã trả bonus chưa');
            $table->timestamps();

            // Index để tối ưu query
            $table->index(['referrer_id', 'tenant_id']);
            $table->index(['referee_id', 'tenant_id']);
            $table->index(['referral_code', 'tenant_id']);
            $table->index(['commission_paid', 'bonus_paid']);

            // Unique constraint để đảm bảo mỗi user chỉ được giới thiệu 1 lần trong 1 tenant
            $table->unique(['referee_id', 'tenant_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('referrals');
    }
};
