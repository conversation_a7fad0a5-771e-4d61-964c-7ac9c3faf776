<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('affiliate_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->cascadeOnDelete();
            $table->boolean('is_enabled')->default(false)->comment('Bật/tắt hệ thống affiliate');
            $table->decimal('referrer_commission_rate', 5, 2)->default(0)->comment('Tỷ lệ hoa hồng cho người giới thiệu (%)');
            $table->decimal('referee_bonus_rate', 5, 2)->default(0)->comment('Tỷ lệ bonus cho người được giới thiệu (%)');
            $table->decimal('min_deposit_for_commission', 15, 2)->default(0)->comment('<PERSON><PERSON> tiền nạp tối thiểu để nhận hoa hồng');
            $table->decimal('max_commission_amount', 15, 2)->nullable()->comment('Số tiền hoa hồng tối đa');
            $table->decimal('max_bonus_amount', 15, 2)->nullable()->comment('Số tiền bonus tối đa');
            $table->integer('commission_valid_days')->default(30)->comment('Số ngày hoa hồng có hiệu lực');
            $table->boolean('require_first_deposit')->default(true)->comment('Yêu cầu người được giới thiệu phải nạp tiền lần đầu');
            $table->timestamps();

            // Index để tối ưu query
            $table->index('tenant_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('affiliate_settings');
    }
};
