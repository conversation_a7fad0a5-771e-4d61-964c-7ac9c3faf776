<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('vouchers', function (Blueprint $table) {
            $table->decimal('min_deposit_amount', 15, 2)->default(0)->after('value')->comment('Số tiền nạp tối thiểu để áp dụng voucher');
            $table->decimal('max_bonus_amount', 15, 2)->nullable()->after('min_deposit_amount')->comment('Số tiền bonus tối đa (cho voucher percent)');
            $table->integer('usage_limit')->nullable()->after('max_bonus_amount')->comment('Giới hạn số lần sử dụng voucher');
            $table->integer('used_count')->default(0)->after('usage_limit')->comment('Số lần đã sử dụng');
            $table->boolean('is_one_time_per_user')->default(false)->after('used_count')->comment('Mỗi user chỉ được sử dụng 1 lần');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('vouchers', function (Blueprint $table) {
            $table->dropColumn([
                'min_deposit_amount',
                'max_bonus_amount',
                'usage_limit',
                'used_count',
                'is_one_time_per_user'
            ]);
        });
    }
};
