<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('commissions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('referral_id')->constrained()->cascadeOnDelete();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete()->comment('Người nhận hoa hồng/bonus');
            $table->foreignId('tenant_id')->constrained()->cascadeOnDelete();
            $table->foreignId('transaction_id')->nullable()->constrained()->nullOnDelete()->comment('Transaction nạp tiền gây ra hoa hồng');
            $table->string('type')->comment('commission hoặc bonus');
            $table->decimal('amount', 15, 2)->comment('Số tiền hoa hồng/bonus');
            $table->decimal('rate', 5, 2)->comment('Tỷ lệ % được áp dụng');
            $table->decimal('base_amount', 15, 2)->comment('Số tiền gốc để tính hoa hồng');
            $table->string('status')->default('pending')->comment('pending, paid, cancelled');
            $table->text('description')->nullable();
            $table->timestamp('earned_at')->useCurrent()->comment('Thời gian kiếm được hoa hồng');
            $table->timestamp('paid_at')->nullable()->comment('Thời gian trả hoa hồng');
            $table->timestamps();

            // Index để tối ưu query
            $table->index(['user_id', 'tenant_id', 'status']);
            $table->index(['referral_id', 'type']);
            $table->index(['status', 'earned_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('commissions');
    }
};
