<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Referral extends Model
{
    protected $fillable = [
        'referrer_id',
        'referee_id',
        'tenant_id',
        'referral_code',
        'referred_at',
        'first_deposit_at',
        'first_deposit_amount',
        'commission_paid',
        'bonus_paid'
    ];

    protected $casts = [
        'referred_at' => 'datetime',
        'first_deposit_at' => 'datetime',
        'first_deposit_amount' => 'decimal:2',
        'commission_paid' => 'boolean',
        'bonus_paid' => 'boolean'
    ];

    public function referrer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    public function referee(): BelongsTo
    {
        return $this->belongsTo(User::class, 'referee_id');
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function commissions(): HasMany
    {
        return $this->hasMany(Commission::class);
    }

    /**
     * Đánh dấu đã nạp tiền lần đầu
     */
    public function markFirstDeposit(float $amount): void
    {
        $this->update([
            'first_deposit_at' => now(),
            'first_deposit_amount' => $amount
        ]);
    }

    /**
     * Đánh dấu đã trả hoa hồng
     */
    public function markCommissionPaid(): void
    {
        $this->update(['commission_paid' => true]);
    }

    /**
     * Đánh dấu đã trả bonus
     */
    public function markBonusPaid(): void
    {
        $this->update(['bonus_paid' => true]);
    }

    /**
     * Kiểm tra có đủ điều kiện nhận hoa hồng không
     */
    public function isEligibleForCommission(): bool
    {
        return $this->first_deposit_at !== null && !$this->commission_paid;
    }

    /**
     * Kiểm tra có đủ điều kiện nhận bonus không
     */
    public function isEligibleForBonus(): bool
    {
        return $this->first_deposit_at !== null && !$this->bonus_paid;
    }
}
