<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AffiliateSetting extends Model
{
    protected $fillable = [
        'tenant_id',
        'is_enabled',
        'referrer_commission_rate',
        'referee_bonus_rate',
        'min_deposit_for_commission',
        'max_commission_amount',
        'max_bonus_amount',
        'commission_valid_days',
        'require_first_deposit'
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'require_first_deposit' => 'boolean',
        'referrer_commission_rate' => 'decimal:2',
        'referee_bonus_rate' => 'decimal:2',
        'min_deposit_for_commission' => 'decimal:2',
        'max_commission_amount' => 'decimal:2',
        'max_bonus_amount' => 'decimal:2',
        'commission_valid_days' => 'integer'
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Tính to<PERSON> hoa hồng cho người giới thiệu
     */
    public function calculateCommission(float $depositAmount): float
    {
        if (!$this->is_enabled || $depositAmount < $this->min_deposit_for_commission) {
            return 0;
        }

        $commission = $depositAmount * ($this->referrer_commission_rate / 100);

        if ($this->max_commission_amount && $commission > $this->max_commission_amount) {
            $commission = $this->max_commission_amount;
        }

        return round($commission, 2);
    }

    /**
     * Tính toán bonus cho người được giới thiệu
     */
    public function calculateBonus(float $depositAmount): float
    {
        if (!$this->is_enabled || $depositAmount < $this->min_deposit_for_commission) {
            return 0;
        }

        $bonus = $depositAmount * ($this->referee_bonus_rate / 100);

        if ($this->max_bonus_amount && $bonus > $this->max_bonus_amount) {
            $bonus = $this->max_bonus_amount;
        }

        return round($bonus, 2);
    }
}
