<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Commission extends Model
{
    const TYPE_COMMISSION = 'commission';
    const TYPE_BONUS = 'bonus';

    const STATUS_PENDING = 'pending';
    const STATUS_PAID = 'paid';
    const STATUS_CANCELLED = 'cancelled';

    protected $fillable = [
        'referral_id',
        'user_id',
        'tenant_id',
        'transaction_id',
        'type',
        'amount',
        'rate',
        'base_amount',
        'status',
        'description',
        'earned_at',
        'paid_at'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'rate' => 'decimal:2',
        'base_amount' => 'decimal:2',
        'earned_at' => 'datetime',
        'paid_at' => 'datetime'
    ];

    public function referral(): BelongsTo
    {
        return $this->belongsTo(Referral::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    public function transaction(): BelongsTo
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Đánh dấu đã trả hoa hồng
     */
    public function markAsPaid(): void
    {
        $this->update([
            'status' => self::STATUS_PAID,
            'paid_at' => now()
        ]);
    }

    /**
     * Hủy hoa hồng
     */
    public function cancel(): void
    {
        $this->update(['status' => self::STATUS_CANCELLED]);
    }

    /**
     * Scope để lấy hoa hồng chưa trả
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope để lấy hoa hồng đã trả
     */
    public function scopePaid($query)
    {
        return $query->where('status', self::STATUS_PAID);
    }

    /**
     * Scope để lấy theo loại
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }
}
