<?php

namespace App\Models;

use App\Enums\VoucherTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Voucher extends Model
{
    protected $table = 'vouchers';

    protected $fillable = [
        'tenant_id',
        'code',
        'type',
        'value',
        'min_deposit_amount',
        'max_bonus_amount',
        'usage_limit',
        'used_count',
        'is_one_time_per_user',
        'start_date',
        'end_date',
        'is_active'
    ];

    protected $casts = [
        'type' => VoucherTypeEnum::class,
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'is_active' => 'boolean',
        'is_one_time_per_user' => 'boolean',
        'value' => 'decimal:2',
        'min_deposit_amount' => 'decimal:2',
        'max_bonus_amount' => 'decimal:2',
        'usage_limit' => 'integer',
        'used_count' => 'integer'
    ];

    /**
     * Relationship với Tenant
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Kiểm tra voucher có còn hiệu lực không
     */
    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();

        if ($this->start_date && $now->lt($this->start_date)) {
            return false;
        }

        if ($this->end_date && $now->gt($this->end_date)) {
            return false;
        }

        return true;
    }

    /**
     * Tự động tạo code voucher nếu chưa có
     */
    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($voucher) {
            if (empty($voucher->code)) {
                do {
                    $code = 'VC' . strtoupper(uniqid());
                } while (self::where('code', $code)->exists());

                $voucher->code = $code;
            }
        });
    }

    /**
     * Scope để lấy voucher còn hiệu lực
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope để lấy voucher theo tenant
     */
    public function scopeForTenant($query, $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Relationship với VoucherUsage
     */
    public function usages(): HasMany
    {
        return $this->hasMany(VoucherUsage::class);
    }

    /**
     * Kiểm tra voucher có thể sử dụng với số tiền nạp không
     */
    public function canUseWithAmount(float $amount): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Kiểm tra số tiền tối thiểu
        if ($amount < $this->min_deposit_amount) {
            return false;
        }

        // Kiểm tra giới hạn số lần sử dụng
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Kiểm tra user đã sử dụng voucher này chưa
     */
    public function hasBeenUsedByUser(int $userId): bool
    {
        if (!$this->is_one_time_per_user) {
            return false;
        }

        return $this->usages()->where('user_id', $userId)->exists();
    }

    /**
     * Tính toán bonus amount
     */
    public function calculateBonus(float $depositAmount): float
    {
        if (!$this->canUseWithAmount($depositAmount)) {
            return 0;
        }

        $bonus = 0;

        if ($this->type === VoucherTypeEnum::PERCENT) {
            $percentValue = min($this->value, 100);
            $bonus = $depositAmount * ($percentValue / 100);

            // Áp dụng giới hạn bonus tối đa nếu có
            if ($this->max_bonus_amount && $bonus > $this->max_bonus_amount) {
                $bonus = $this->max_bonus_amount;
            }
        } elseif ($this->type === VoucherTypeEnum::FIXED) {
            $bonus = $this->value;
        }

        return round($bonus, 2);
    }

    /**
     * Đánh dấu voucher đã được sử dụng
     */
    public function markAsUsed(): void
    {
        $this->increment('used_count');
    }
}
