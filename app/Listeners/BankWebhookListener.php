<?php

namespace App\Listeners;

use App\Enums\TransactionStatusEnum;
use App\Enums\TransactionTypeEnum;
use App\Enums\TypeNotificationEnum;
use App\Events\BankWebhookEvent;
use App\Models\Notification;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Voucher;
use App\Models\VoucherUsage;
use Illuminate\Support\Facades\DB;

class BankWebhookListener
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BankWebhookEvent $event): void
    {
        if (strtolower($event->bankWebhookData->transferType) !== 'in') {
            return;
        }

        $data = $event->bankWebhookData;
        $user = User::query()->where('code', $event->info)->first();

        if (!$user) {
            return;
        }

        DB::transaction(function () use ($user, $data) {
            $amount = $data->transferAmount;
            $tenantId = optional($user->tenants()->first())->id ?? config('app.default_tenant_id');

            // Tìm voucher hiệu lực cho tenant
            $voucher = Voucher::query()
                ->forTenant($tenantId)
                ->active()
                ->where('start_date', '<=', now())
                ->where('end_date', '>=', now())
                ->first();

            $bonus = 0;
            $voucherApplied = null;

            // Áp dụng voucher nếu có và hợp lệ
            if ($voucher && $voucher->canUseWithAmount($amount) && !$voucher->hasBeenUsedByUser($user->id)) {
                $bonus = $voucher->calculateBonus($amount);

                if ($bonus > 0) {
                    $voucherApplied = $voucher;
                }
            }

            $totalAmount = $amount + $bonus;
            $balanceBefore = $user->balance;
            $balanceAfter = $balanceBefore + $totalAmount;

            // Cập nhật balance và total_deposit
            $user->update([
                'balance' => $balanceAfter,
                'total_deposit' => $user->total_deposit + $amount, // Chỉ tính tiền gốc vào total_deposit
            ]);


            // Tạo transaction với thông tin chi tiết
            $description = 'Nạp tiền qua ' . $data->gateway;
            $metadata = [
                'original_amount' => $amount,
                'gateway' => $data->gateway,
                'transaction_number' => $data->transactionNumber ?? null,
                'reference_code' => $data->referenceCode ?? null,
            ];

            if ($voucherApplied) {
                $description .= ' (Bonus: ' . number_format($bonus) . ' từ voucher ' . $voucherApplied->code . ')';
                $metadata['voucher'] = [
                    'id' => $voucherApplied->id,
                    'code' => $voucherApplied->code,
                    'type' => $voucherApplied->type->value,
                    'value' => $voucherApplied->value,
                    'bonus_amount' => $bonus,
                ];
            }

            $transaction = Transaction::query()->create([
                'user_id' => $user->id,
                'tenant_id' => $tenantId,
                'admin_id' => null, // Không phải admin tạo
                'type' => TransactionTypeEnum::DEPOSIT->value,
                'math' => '+',
                'amount' => $totalAmount,
                'balance_before' => $balanceBefore,
                'balance_after' => $balanceAfter,
                'status' => TransactionStatusEnum::COMPLETED->value,
                'description' => $description,
                'metadata' => $metadata,
            ]);

            // Tạo record voucher usage nếu có voucher được áp dụng
            if ($voucherApplied && $bonus > 0) {
                VoucherUsage::create([
                    'voucher_id' => $voucherApplied->id,
                    'user_id' => $user->id,
                    'tenant_id' => $tenantId,
                    'transaction_id' => $transaction->id,
                    'deposit_amount' => $amount,
                    'bonus_amount' => $bonus,
                    'used_at' => now(),
                ]);

                // Cập nhật số lần sử dụng voucher
                $voucherApplied->markAsUsed();
            }

            // Tạo notification với thông tin chi tiết
            $notificationContent = 'Nạp thành công <span class="text-orange fw-semibold">'.number_format($amount).'</span>';

            if ($voucherApplied && $bonus > 0) {
                $notificationContent .= ' + Bonus <span class="text-success fw-semibold">'.number_format($bonus).'</span> từ voucher ' . $voucherApplied->code;
                $notificationContent .= '<br>Tổng cộng: <span class="text-primary fw-semibold">'.number_format($totalAmount).'</span>';
            }

            Notification::query()->create([
                'title' => 'Nạp tiền thành công',
                'content' => $notificationContent,
                'tenant_id' => $tenantId,
                'user_id' => $user->id,
                'is_read' => false,
                'amount' => $totalAmount,
                'type' => TypeNotificationEnum::DEPOSIT->value,
            ]);
        });
    }

}
