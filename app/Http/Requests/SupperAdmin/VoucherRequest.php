<?php

namespace App\Http\Requests\SupperAdmin;

use App\Enums\VoucherTypeEnum;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class VoucherRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $voucherId = $this->route('voucher') ? $this->route('voucher') : null;

        return [
            'code' => [
                'required',
                'string',
                'max:255',
                Rule::unique('vouchers', 'code')->ignore($voucherId)
            ],
            'tenant_id' => [
                'required',
                'exists:tenants,id'
            ],
            'type' => [
                'required',
                'string',
                Rule::in(array_column(VoucherTypeEnum::cases(), 'value'))
            ],
            'value' => [
                'required',
                'numeric',
                'min:0',
                'max:999999999.99'
            ],
            'min_deposit_amount' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999999.99'
            ],
            'max_bonus_amount' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999999.99'
            ],
            'usage_limit' => [
                'nullable',
                'integer',
                'min:1'
            ],
            'is_one_time_per_user' => [
                'boolean'
            ],
            'start_date' => [
                'nullable',
                'string'
            ],
            'end_date' => [
                'nullable',
                'string'
            ],
            'is_active' => [
                'boolean'
            ]
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'code' => 'Mã voucher',
            'tenant_id' => 'Website',
            'type' => 'Loại voucher',
            'value' => 'Giá trị',
            'start_date' => 'Ngày bắt đầu',
            'end_date' => 'Ngày kết thúc',
            'is_active' => 'Trạng thái'
        ];
    }

    /**
     * Get custom error messages.
     */
    public function messages(): array
    {
        return [
            'code.required' => 'Mã voucher là bắt buộc.',
            'code.unique' => 'Mã voucher đã tồn tại.',
            'tenant_id.required' => 'Vui lòng chọn website.',
            'tenant_id.exists' => 'Website không tồn tại.',
            'type.required' => 'Vui lòng chọn loại voucher.',
            'type.in' => 'Loại voucher không hợp lệ.',
            'value.required' => 'Giá trị voucher là bắt buộc.',
            'value.numeric' => 'Giá trị voucher phải là số.',
            'value.min' => 'Giá trị voucher phải lớn hơn hoặc bằng 0.',
            'value.max' => 'Giá trị voucher quá lớn.',
            'min_deposit_amount.numeric' => 'Số tiền nạp tối thiểu phải là số.',
            'min_deposit_amount.min' => 'Số tiền nạp tối thiểu phải lớn hơn hoặc bằng 0.',
            'min_deposit_amount.max' => 'Số tiền nạp tối thiểu quá lớn.',
            'max_bonus_amount.numeric' => 'Số tiền bonus tối đa phải là số.',
            'max_bonus_amount.min' => 'Số tiền bonus tối đa phải lớn hơn hoặc bằng 0.',
            'max_bonus_amount.max' => 'Số tiền bonus tối đa quá lớn.',
            'usage_limit.integer' => 'Giới hạn sử dụng phải là số nguyên.',
            'usage_limit.min' => 'Giới hạn sử dụng phải lớn hơn 0.',
            'start_date.date' => 'Ngày bắt đầu không hợp lệ.',
            'start_date.after_or_equal' => 'Ngày bắt đầu phải từ hôm nay trở đi.',
            'end_date.date' => 'Ngày kết thúc không hợp lệ.',
            'end_date.after' => 'Ngày kết thúc phải sau ngày bắt đầu.',
        ];
    }

    /**
     * Prepare the data for validation.
     */

}
